import requests
from bs4 import BeautifulSoup
from datetime import datetime
import json
import sys
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# 配置
CONFIG = {
    'api_key': "c9a4bf873cdff7e68c76a6bf404af03a",
    'proxies': {
        "http": "http://192.168.31.72:7897",
        "https": "http://192.168.31.72:7897"
    },
    'base_url': "https://api.themoviedb.org/3",
    'output_file': "剧集更新.json"
}

# 实时输出函数
def print_flush(*args, **kwargs):
    print(*args, **kwargs)
    sys.stdout.flush()

class EpisodeTracker:
    def __init__(self):
        self.today = datetime.now().strftime('%Y-%m-%d')
        self.current_year = datetime.now().strftime('%Y')
        self.results = []
        self.display_count = 0
        self.header_printed = False
        self.lock = threading.Lock()
        
    def get_with_retry(self, url, params=None, max_retries=3):
        """发送GET请求并支持重试"""
        for attempt in range(max_retries):
            try:
                response = requests.get(url, params=params, timeout=30, proxies=CONFIG['proxies'])
                response.raise_for_status()
                return response
            except:
                if attempt == max_retries - 1:
                    return None
        return None
    
    def get_maoyan_hot_shows(self):
        """获取猫眼热门电影列表"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        try:
            response = requests.get('https://piaofang.maoyan.com/web-heat', headers=headers, timeout=15)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                movies = soup.select('tbody.table-body tr.body-row')
                return [movie.select_one('p.video-name').text for movie in movies]
        except:
            pass
        return []
    
    def search_tv_show(self, show_name):
        """搜索电视剧并返回匹配结果"""
        search_url = f"{CONFIG['base_url']}/search/tv"
        params = {
            "api_key": CONFIG['api_key'],
            "query": show_name,
            "language": "zh-CN",
            "first_air_date_year": self.current_year
        }
        
        response = self.get_with_retry(search_url, params)
        if not response or response.status_code != 200:
            return None
            
        results = response.json().get('results', [])
        # 寻找精确匹配
        for result in results:
            if result.get('name', '').strip() == show_name.strip():
                return result
        return None
    
    def get_tv_details(self, tv_id):
        """获取电视剧详细信息包括剧集列表"""
        details_url = f"{CONFIG['base_url']}/tv/{tv_id}"
        params = {
            "api_key": CONFIG['api_key'],
            "language": "zh-CN"
        }
        
        response = self.get_with_retry(details_url, params)
        if not response or response.status_code != 200:
            return None
            
        details = response.json()
        
        # 获取最新季的剧集信息
        season_num = details.get('number_of_seasons', 1)
        season_url = f"{CONFIG['base_url']}/tv/{tv_id}/season/{season_num}"
        season_response = self.get_with_retry(season_url, params)
        
        if season_response and season_response.status_code == 200:
            season_data = season_response.json()
            details['episodes'] = season_data.get('episodes', [])
        
        return details
    
    def get_platform_info(self, details):
        """从详情中提取播放平台信息"""
        platforms = []
        homepage = details.get('homepage', '').lower()
        
        platform_map = {
            'iqiyi.com': '爱奇艺',
            'youku.com': '优酷', 
            'qq.com': '腾讯',
            'wetv.vip': '腾讯',
            'mgtv.com': '芒果TV'
        }
        
        for domain, platform in platform_map.items():
            if domain in homepage:
                platforms.append(platform)
                break
                
        return platforms
    
    def has_today_episodes(self, details):
        """检查是否有今天播出的剧集"""
        episodes = details.get('episodes', [])
        return any(ep.get('air_date') == self.today for ep in episodes)
    
    def get_today_episodes(self, details):
        """获取今天播出的剧集编号"""
        episodes = details.get('episodes', [])
        today_eps = [ep.get('episode_number') for ep in episodes 
                    if ep.get('air_date') == self.today and ep.get('episode_number')]
        return sorted(today_eps)
    
    def process_show(self, show_name):
        """处理单个剧集"""
        try:
            # 搜索剧集
            search_result = self.search_tv_show(show_name)
            if not search_result:
                return None
                
            # 获取详细信息
            details = self.get_tv_details(search_result['id'])
            if not details or not self.has_today_episodes(details):
                return None
                
            # 提取信息
            platforms = self.get_platform_info(details)
            today_eps = self.get_today_episodes(details)
            
            return {
                'name': details.get('name', show_name),
                'total_episodes': details.get('number_of_episodes', 0),
                'today_episodes': today_eps,
                'platforms': platforms
            }
        except:
            return None
    
    def format_output(self, show_data, index):
        """格式化输出单个剧集信息（线程安全）"""
        with self.lock:
            # 打印头部分隔线（如果还没打印过）
            if not self.header_printed:
                print_flush("========================")
                self.header_printed = True

            name = show_data['name']
            total = show_data['total_episodes']
            episodes = show_data['today_episodes']
            platforms = show_data['platforms']

            total_text = f"--共{total}集" if total > 0 else ""
            platform_text = f"（{' | '.join(platforms)}）" if platforms else ""
            episode_text = "、".join([str(ep) for ep in episodes])

            print_flush(f"{index}. {name}{total_text}{platform_text}")
            print_flush(f"今日更新第：{episode_text}集")
            print_flush("--------------------------------")
    
    def save_results(self):
        """保存结果到JSON文件"""
        if self.results:
            try:
                output_data = []
                for show in self.results:
                    output_data.append({
                        "name": show['name'],
                        "total_episodes": show['total_episodes'],
                        "episodes": "、".join([str(ep) for ep in show['today_episodes']])
                    })
                
                with open(CONFIG['output_file'], 'w', encoding='utf-8') as f:
                    json.dump(output_data, f, ensure_ascii=False, indent=2)
                print(f"剧集信息已保存到 {CONFIG['output_file']}")
            except:
                pass
    
    def run(self):
        """主运行函数（实时输出版本）"""
        print_flush("          今日热门剧集更新日历")

        # 获取热门剧集列表
        hot_shows = self.get_maoyan_hot_shows()
        if not hot_shows:
            print_flush("今日暂无更新剧集")
            return

        # 并行处理所有剧集，使用 as_completed 实现实时输出
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = {executor.submit(self.process_show, show): show for show in hot_shows}

            for future in as_completed(futures):
                result = future.result()
                if result:
                    self.results.append(result)
                    self.format_output(result, len(self.results))

        # 输出结束分隔线和保存文件
        with self.lock:
            if self.header_printed:
                print_flush("========================")
                self.save_results()
            else:
                print_flush("今日暂无更新剧集")

if __name__ == "__main__":
    try:
        tracker = EpisodeTracker()
        tracker.run()
    except:
        print("今日暂无更新剧集")
