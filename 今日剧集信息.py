import requests
import time
from bs4 import BeautifulSoup
import logging
from datetime import datetime
import json
from pathlib import Path
import sys
import threading
import queue
from concurrent.futures import ThreadPoolExecutor

# 添加代理设置（根据您的代理情况修改）
# 如果您使用的是HTTP代理
HTTP_PROXY = "http://192.168.31.72:7897"  # 请修改为您的实际代理地址和端口
HTTPS_PROXY = "http://192.168.31.72:7897"  # 请修改为您的实际代理地址和端口

# 创建代理字典
PROXIES = {
    "http": HTTP_PROXY,
    "https": HTTPS_PROXY
}

# 禁用输出缓冲，确保实时显示输出
try:
    sys.stdout.reconfigure(line_buffering=True)  # Python 3.7+
except AttributeError:
    # 对于低于Python 3.7的版本
    sys.stdout = open(sys.stdout.fileno(), mode='w', buffering=1)

# 自定义print函数，确保立即刷新输出
def print_flush(*args, **kwargs):
    print(*args, **kwargs)
    sys.stdout.flush()  # 强制刷新输出缓冲区

# 创建线程安全的打印锁，确保输出不会混乱
print_lock = threading.Lock()

# 创建队列用于存储找到的剧集
episode_queue = queue.Queue()

# 用于标记是否已打印头部分隔线
header_printed = threading.Event()

# 打印结果的函数，在单独线程中运行
def print_results():
    display_count = 0
    shows_data = []

    while True:
        try:
            # 从队列获取结果，等待1秒
            item = episode_queue.get(timeout=1)

            # 如果收到结束信号，跳出循环
            if item == "END":
                break

            # 获取剧集信息
            correct_item, is_last = item

            # 打印头部分隔线（如果还没打印过）
            with print_lock:
                if not header_printed.is_set():
                    print_flush("========================")
                    header_printed.set()

                display_count += 1
                # 显示剧集信息
                show_info = display_media_details(
                    correct_item,
                    display_count,
                    is_first=False,
                    is_last=is_last
                )
                if show_info:
                    shows_data.append(show_info)

            # 标记任务完成
            episode_queue.task_done()
        except queue.Empty:
            # 队列暂时为空，继续等待
            continue

    # 如果找到了剧集，打印尾部分隔线
    with print_lock:
        if header_printed.is_set():
            print_flush("========================")
        else:
            print_flush("今日暂无更新剧集")

    # 将数据写入JSON文件
    if shows_data:
        try:
            json_path = Path("E:/Python/项目文件/剧集更新.json")
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(shows_data, f, ensure_ascii=False, indent=2)
            print_flush(f"剧集信息已保存到 {json_path}")
        except:
            # 隐藏文件保存错误
            pass

# 单个作品处理函数
def process_media(media_name, api_key, year):
    try:
        correct_item = find_correct_media(media_name, api_key, target_year=year)
        if correct_item and has_today_episodes(correct_item):
            # 找到更新的剧集，加入队列
            episode_queue.put((correct_item, False))
            return True
    except:
        # 完全隐藏所有错误
        pass
    return False

def get_with_retry(url, headers=None, params=None, max_retries=3, retry_delay=2):
    """发送GET请求并支持重试"""
    for attempt in range(max_retries):
        try:
            # 不输出请求日志
            # 使用代理
            response = requests.get(url, headers=headers, params=params, timeout=30, proxies=PROXIES)
            response.raise_for_status()
            return response
        except (requests.RequestException, ConnectionError):
            if attempt == max_retries - 1:
                # 不显示任何错误信息
                return None
            time.sleep(retry_delay)

def get_tmdb_id_by_tv_name(tv_name, api_key):
    """
    通过电视剧名称搜索并获取TMDB ID
    
    参数:
        tv_name (str): 电视剧名称
        api_key (str): TMDB API密钥
        
    返回:
        dict: 包含搜索结果的字典，如果找到匹配项则包含ID
    """
    base_url = "https://api.themoviedb.org/3"
    search_endpoint = f"{base_url}/search/tv"
    
    params = {
        "api_key": api_key,
        "query": tv_name,
        "language": "zh-CN"  # 可以根据需要更改语言
    }
    
    # 使用代理
    response = requests.get(search_endpoint, params=params, proxies=PROXIES)
    
    if response.status_code == 200:
        results = response.json()
        if results["total_results"] > 0:
            # 返回第一个匹配结果
            first_result = results["results"][0]
            return {
                "id": first_result["id"],
                "name": first_result["name"],
                "original_name": first_result.get("original_name", ""),
                "overview": first_result.get("overview", ""),
                "first_air_date": first_result.get("first_air_date", "")
            }
        else:
            return {"error": "未找到匹配的电视剧"}
    else:
        return {"error": f"API请求失败: {response.status_code}"}

def search_media(media_name, api_key, media_type="tv", year=None):
    """搜索电视剧或电影并返回详细结果供区分选择，支持年份筛选"""
    base_url = "https://api.themoviedb.org/3"
    search_endpoint = f"{base_url}/search/{media_type}"
    
    params = {
        "api_key": api_key,
        "query": media_name,
        "language": "zh-CN"
    }
    
    # 如果指定了年份，添加首播/上映年份过滤
    if year:
        if media_type == "tv":
            params["first_air_date_year"] = year
        else:  # movie
            params["primary_release_year"] = year
    
    response = get_with_retry(search_endpoint, params=params)
    
    if response is None:
        # 不输出API请求失败信息
        return []
        
    if response.status_code == 200:
        results = response.json()
        if results["total_results"] > 0:
            all_results = results["results"]
            
            # 首先尝试精确匹配
            name_field = 'name' if media_type == 'tv' else 'title'
            exact_matches = [
                item for item in all_results 
                if item.get(name_field, '').lower() == media_name.lower()
            ]
            
            # 如果有精确匹配的结果，只返回精确匹配的
            if exact_matches:
                return exact_matches
            
            # 否则返回所有结果
            return all_results
        else:
            return []
    else:
        # 不输出API请求失败信息
        return []

def search_all_media(media_name, api_key, year=None):
    """同时搜索电视剧和电影，只返回完全匹配的结果"""
    # 搜索电视剧
    tv_results = search_media(media_name, api_key, "tv", year)
    
    # 搜索电影
    movie_results = search_media(media_name, api_key, "movie", year)
    
    # 为每个结果添加媒体类型标记
    for result in tv_results:
        result['media_type'] = 'tv'
    
    for result in movie_results:
        result['media_type'] = 'movie'
    
    # 合并结果
    all_results = tv_results + movie_results
    
    # 只返回完全匹配的结果
    exact_matches = [
        item for item in all_results
        if (item.get('name', '').strip() if item['media_type'] == 'tv' else item.get('title', '').strip()) == media_name.strip()
    ]
    
    # 按流行度排序
    if exact_matches:
        exact_matches.sort(key=lambda x: x.get('popularity', 0), reverse=True)
        return exact_matches
    
    # 如果没有完全匹配的结果，返回空列表
    return []

def filter_by_year(results, year):
    """手动过滤指定年份的结果（因为API的year参数有时不够精确）"""
    filtered_results = []
    
    for item in results:
        media_type = item.get('media_type', 'tv')
        date_field = 'first_air_date' if media_type == 'tv' else 'release_date'
        date = item.get(date_field, '')
        if date and date.startswith(str(year)):
            filtered_results.append(item)
    return filtered_results

def display_media_options(results):
    """展示更详细的电视剧或电影信息以便区分"""
    if not results:
        return None
    
    # 如果只有一个结果，直接返回
    if len(results) == 1:
        return results[0]
        
    # 如果有多个完全匹配的结果，返回第一个（已按流行度排序）
    return results[0]

def get_media_details(item_id, api_key, media_type="tv", target_season=1):
    """获取电视剧或电影的详细信息"""
    base_url = "https://api.themoviedb.org/3"
    details_endpoint = f"{base_url}/{media_type}/{item_id}"
    
    params = {
        "api_key": api_key,
        "language": "zh-CN",
        "append_to_response": "credits,external_ids"  # 获取演员和外部ID信息
    }
    
    # 获取中文详情
    response = get_with_retry(details_endpoint, params=params)
    
    # 获取英文详情（用于获取准确的homepage）
    params["language"] = "en-US"
    en_response = get_with_retry(details_endpoint, params=params)
    
    if response and response.status_code == 200 and en_response and en_response.status_code == 200:
        details = response.json()
        en_details = en_response.json()
        
        # 添加英文版本中的homepage和networks信息
        details['homepage'] = en_details.get('homepage', '')
        details['networks'] = en_details.get('networks', [])
        
        # 如果是电视剧，获取指定季的播出时间表
        if media_type == "tv" and details.get('number_of_seasons', 0) > 0:
            all_episodes = []
            total_seasons = details.get('number_of_seasons', 0)
            
            # 如果指定的季数超过总季数，使用最后一季
            if target_season > total_seasons:
                target_season = total_seasons
            
            # 获取指定季的信息
            season_endpoint = f"{base_url}/tv/{item_id}/season/{target_season}"
            season_params = {
                "api_key": api_key,
                "language": "zh-CN"
            }
            season_response = get_with_retry(season_endpoint, params=season_params)
            if season_response and season_response.status_code == 200:
                season_data = season_response.json()
                episodes = season_data.get('episodes', [])
                # 为每个剧集添加季数信息
                for ep in episodes:
                    ep['season_number'] = target_season
                all_episodes.extend(episodes)
            
            # 按播出日期排序，处理None值
            def sort_key(x):
                air_date = x.get('air_date')
                return air_date if air_date is not None else '9999-12-31'
            
            all_episodes.sort(key=sort_key)
            details['all_episodes'] = [ep for ep in all_episodes if ep.get('air_date') is not None]
        
        return details
    else:
        # 不输出详情请求失败信息
        return None

def get_platform_info(details):
    """从详情中提取播放平台信息"""
    platforms = []
    
    # 从homepage判断平台
    homepage = details.get('homepage', '').lower()
    if 'iqiyi.com' in homepage:
        platforms.append('爱奇艺')
    elif 'youku.com' in homepage:
        platforms.append('优酷')
    elif 'wetv.vip' in homepage or 'qq.com' in homepage:
        platforms.append('腾讯')
    elif 'mgtv.com' in homepage:
        platforms.append('芒果TV')
    
    # 从networks判断平台
    networks = details.get('networks', [])
    network_names = {
        'iQiyi': '爱奇艺',
        'Youku': '优酷',
        'Tencent Video': '腾讯',
        'WeTV': '腾讯',
        'Mango TV': '芒果TV'
    }
    
    for network in networks:
        name = network.get('name')
        if name in network_names and network_names[name] not in platforms:
            platforms.append(network_names[name])
    
    return platforms

def display_media_details(correct_item, index=None, is_first=False, is_last=False):
    """显示媒体详细信息，只显示当天播出的内容，并返回剧集信息"""
    if not correct_item:
        return None
        
    today = datetime.now().strftime('%Y-%m-%d')
    media_type = correct_item.get('media_type', 'tv')
    name_field = 'name' if media_type == 'tv' else 'title'
    original_name_field = 'original_name' if media_type == 'tv' else 'original_title'
    
    # 获取中英文名称
    chinese_name = correct_item.get(name_field, '未知')
    
    # 只处理电视剧且当天有播出的内容
    if media_type == 'tv':
        all_episodes = correct_item.get('all_episodes', [])
        today_episodes = [ep for ep in all_episodes if ep.get('air_date') == today]
        
        if today_episodes:
            # 获取播放平台信息
            platforms = get_platform_info(correct_item)
            platform_text = f"（{' | '.join(platforms)}）" if platforms else ""
            
            # 获取总集数
            total_episodes = correct_item.get('number_of_episodes', 0)
            total_episodes_text = f"--共{total_episodes}集" if total_episodes > 0 else ""
            
            # 输出按照示例格式
            print_flush(f"{index}. {chinese_name}{total_episodes_text}{platform_text}")
            # print_flush(f"TMDB：https://www.themoviedb.org/{media_type}/{correct_item.get('id')}")
            
            # 按季数分组（仅用于显示）
            season_episodes = {}
            for ep in today_episodes:
                season_num = ep.get('season_number', '未知')
                ep_num = ep.get('episode_number', '未知')
                if season_num not in season_episodes:
                    season_episodes[season_num] = []
                season_episodes[season_num].append(ep_num)
            
            # 格式化输出今日播出信息
            season_texts = []
            for season_num in sorted(season_episodes.keys()):
                eps = season_episodes[season_num]
                episode_list = "、".join([str(ep) for ep in sorted(eps)])
                season_texts.append(f"第：{episode_list}集")
            
            print_flush(f"今日更新{' | '.join(season_texts)}")
            
            # 添加分隔线
            if not is_last:
                print_flush("--------------------------------")
                
            # 收集所有集数用于JSON输出
            all_episode_numbers = []
            for ep in today_episodes:
                ep_num = ep.get('episode_number', '未知')
                if ep_num != '未知':
                    all_episode_numbers.append(ep_num)

            # 返回简化的剧集信息，使用顿号连接集数
            return {
                "name": chinese_name,
                "total_episodes": total_episodes,
                "episodes": "、".join([str(ep) for ep in sorted(all_episode_numbers)])
            }
    return None

def find_correct_media(media_name, api_key, target_year=None, target_season=1):
    """完整流程：智能搜索、筛选、确认正确的电视剧或电影，支持年份筛选"""
    # 第一步：同时搜索电视剧和电影
    results = search_all_media(media_name, api_key, year=target_year)
    
    if not results:
        return None
    
    # 如果指定了年份但API没有正确过滤，手动过滤
    if target_year and len(results) > 0:
        filtered_results = filter_by_year(results, target_year)
        if filtered_results:
            results = filtered_results
    
    # 第二步：自动选择最匹配的结果
    selected_item = display_media_options(results)
    if not selected_item:
        return None
    
    # 第三步：获取详细信息
    media_type = selected_item.get('media_type', 'tv')
    details = get_media_details(selected_item['id'], api_key, media_type, target_season)
    
    if details:
        # 确保保留原始搜索结果中的基本信息
        details['media_type'] = media_type
        details['id'] = selected_item['id']
        return details
    return None

def get_maoyan_hot_shows():
    """获取猫眼热门电影"""
    headers = {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.0.0',
    }

    try:
        # 不显示获取进度
        # 猫眼网站不需要代理，因为已经能正常访问
        response = requests.get('https://piaofang.maoyan.com/web-heat', headers=headers, timeout=15)
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'html.parser')
            movies = soup.select('tbody.table-body tr.body-row')
            result = [movie.select_one('p.video-name').text for movie in movies]
            return result
        else:
            # 不显示错误信息
            return []
    except Exception:
        # 不显示错误信息
        return []

def has_today_episodes(media_details):
    """检查是否有今天播出的剧集"""
    if not media_details or media_details.get('media_type') != 'tv':
        return False
        
    today = datetime.now().strftime('%Y-%m-%d')
    all_episodes = media_details.get('all_episodes', [])
    
    return any(ep.get('air_date') == today for ep in all_episodes)

# 主程序
if __name__ == "__main__":
    API_KEY = "c9a4bf873cdff7e68c76a6bf404af03a"
    # 使用当前年份而不是未来年份
    DEFAULT_YEAR = datetime.now().strftime('%Y')
    
    # 禁用logging输出到控制台
    logging.basicConfig(level=logging.CRITICAL)  # 只记录致命错误
    
    try:
        # 测试代理是否工作
        try:
            test_response = requests.get("https://api.themoviedb.org/3/configuration", 
                                         params={"api_key": API_KEY}, 
                                         proxies=PROXIES, 
                                         timeout=10)
            if test_response.status_code == 200:
                print_flush("          今日热门剧集更新日历")
            else:
                print_flush(f"TMDB API 连接测试失败，状态码: {test_response.status_code}")
        except Exception as e:
            print_flush(f"TMDB API 连接测试失败: {e}")
            print_flush("请检查代理设置是否正确，程序将继续尝试...")
        
        # 获取猫眼热门列表
        hot_shows = get_maoyan_hot_shows()
        if not hot_shows:
            exit()
        
        # 启动打印结果的线程
        print_thread = threading.Thread(target=print_results)
        print_thread.daemon = True
        print_thread.start()
        
        # 使用线程池并行处理所有作品
        with ThreadPoolExecutor(max_workers=5) as executor:
            # 提交所有任务
            futures = [executor.submit(process_media, show, API_KEY, DEFAULT_YEAR) 
                      for show in hot_shows]
            
            # 等待所有任务完成
            for future in futures:
                future.result()  # 获取结果但不处理
        
        # 发送结束信号给打印线程
        episode_queue.put("END")
        
        # 等待打印线程完成
        print_thread.join()
        
    except:
        # 隐藏所有主程序错误
        print_flush("今日暂无更新剧集")